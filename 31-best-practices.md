# 31 – Panson Global Engineering & Website Best Practices (v1)

Owner: Engineering & Marketing | Review: Quarterly | Last updated: 2025-08-10

---

## 1) Scope & Sources
- Purpose: Document established best practices in the Panson Global project and highlight gaps to address.
- Primary sources:
  - Architecture: `21-website-architecture.md`
  - Strategy & content: `19-website-strategy-design.md`, `20-website-content-design.md`, `Panson Global Website Content Blueprint.md`
  - SEO plan: `30-seo-strategy.md`, `22-seo-keyword-research.md`
  - Services & business logic: `18-comprehensive-services-guide.md`, `11-business-logic-analysis.md`
  - AXP & roadmap: `29-strategy.md`, `05-implementation-roadmap.md`
  - Naming/worklist: `100-panson-dubai-worklist.md`

---

## 2) Architecture & Stack (Current Practices)
- Framework: Next.js + TailwindCSS; CMS: Sanity.io; Hosting: Vercel with ISR.
- IA & components: Well-defined IA, component library, page archetypes (from strategy and blueprint docs).
- Localization: Multi-locale (EN/AR/ZH) planned with hreflang and canonicalization.
- SEO & analytics: JSON-LD schemas, GA4, GSC; HubSpot lead capture.
- Deploy workflow: GitHub → Vercel CI; preview environments; CMS webhooks (from `21-website-architecture.md`).

---

## 3) Content, SEO & Governance (Current Practices)
- Hub-and-spoke structure: Pillar pages + spokes, siloed internal linking.
- On-page templates: Titles/H1s, intro block with CTA, schema types, media via Cloudinary, sticky CTAs (see `30-seo-strategy.md`).
- Programmatic SEO: SKU templating for fasteners; canonical cluster strategy.
- Roles & workflow: SEO Lead, Content Strategist, EN/AR/ZH writers, Dev, Designer, Outreach. Workflow: Brief → Draft → Sanity → QA/Previews → Release → Measure (`30-seo-strategy.md`).
- Measurement: GA4/GSC/HubSpot + Looker Studio dashboard.

---

## 4) Services Operations (Documented Practices)
- Cross-border QC (China + GCC), logistics & inventory, technical support & engineering, PM & coordination, long-term partnership development (`18-comprehensive-services-guide.md`).
- Strategic positioning and value creation logic documented (`11-business-logic-analysis.md`).

---

## 5) Naming & Information Architecture (Documented Practices)
- Category label: Prefer "Solutions Portfolio" for product/service groupings (`100-panson-dubai-worklist.md`).
- Project structure and content foldering defined in the Content Blueprint.

---

## 6) Deployment & DevOps (Current + Partial)
- CI/CD: GitHub → Vercel previews + production; CMS webhooks for content invalidation.
- Visual QA: Chromatic/Storybook noted in architecture/SEO docs for preview QA.
- Monitoring/Status: Mentioned (StatusPage/Slack) in architecture doc at a high level.

---

## 7) Quality, Testing & Accessibility (Gaps Noted)
- No explicit unit/integration/E2E test frameworks documented.
- No codified accessibility checks beyond page templates and performance targets.
- Visual regression is referenced but not fully specified as a gate.

---

## 8) Coding Standards & Repo Workflow (Gaps Noted)
- No ESLint/Prettier/TypeScript conventions or commit/PR policies documented.
- No branching model (trunk/GitFlow) or required checks documented.
- No CODEOWNERS/CONTRIBUTING/PR/Issue templates present.

---

## 9) Recommended Engineering Best Practices (Actionable)
- Code quality & style
  - TypeScript-first with strict mode.
  - ESLint (Next.js + TS + a11y), Prettier, import/order rules.
  - Husky + lint-staged for pre-commit (lint, typecheck, format).
- Testing & QA
  - Unit: Jest + ts-jest.
  - Component: Storybook + Testing Library; Chromatic for visual regression.
  - E2E: Playwright or Cypress; smoke tests on key flows (RFQ, search, locale switch).
  - Accessibility: axe checks in Storybook; pa11y/Playwright a11y CI job.
- CI/CD
  - GitHub Actions: install → lint → typecheck → unit + component tests → build; Chromatic run.
  - Required checks gated before merge; Vercel Preview comments with Lighthouse summary.
- Branching & reviews
  - Trunk-based with short-lived feature branches; Conventional Commits + change scope.
  - CODEOWNERS for app, schema, infra; 1–2 reviewers required; PR template with checklist.
- CMS (Sanity) governance
  - Schema versioning + migrations; preview drafts; content validations (required fields, locales, slugs).
  - Editorial workflow mapped to Sanity roles/permissions; audit fields (createdBy/updatedBy).
- Observability & security
  - Sentry (app + edge functions), logging of key events; uptime monitoring of public endpoints.
  - Secrets via Vercel envs; least-privilege access to Vercel/GitHub/Sanity.
- Performance & SEO
  - Budgets: LCP < 2.5s, CLS < 0.1, TBT < 200ms on top pages (already in `30-seo-strategy.md`).
  - next-seo for meta/canonical/hreflang; image optimization via Cloudinary + Next/Image.
  - Programmatic pages: canonical clusters, robots rules for faceted params.
- i18n
  - next-intl or next-i18next; locale routing and fallback; human-reviewed AR/ZH.

---

## 10) 30/60/90 Implementation Plan
- 30 days
  - Add repo scaffolding: ESLint, Prettier, TS strict, Husky + lint-staged, CODEOWNERS, PR/Issue templates, CONTRIBUTING.md.
  - Set up GitHub Actions pipeline (lint/type/test/build) + Chromatic; Vercel Preview checks.
  - Initialize Storybook; write 10–15 critical component stories.
- 60 days
  - Add Jest + RTL coverage to critical components; add Playwright smoke suite.
  - Sanity schema governance: versioning + migration scripts; content validations for pillars.
  - Add Sentry + basic dashboards; Looker/GA4 SEO dashboard live.
- 90 days
  - Expand E2E to critical user journeys; accessibility CI in place.
  - Performance budgets enforced in CI; improve CLS/LCP where needed.
  - Programmatic SKU pages shipped with canonical groups and Algolia finder.

---

## 11) References
- Architecture & ops manual: `21-website-architecture.md`
- SEO strategy: `30-seo-strategy.md`
- Strategy/design/content: `19-website-strategy-design.md`, `20-website-content-design.md`, `Panson Global Website Content Blueprint.md`
- Services & positioning: `18-comprehensive-services-guide.md`, `11-business-logic-analysis.md`
- Roadmap & AXP: `05-implementation-roadmap.md`, `29-strategy.md`
- Naming/worklist: `100-panson-dubai-worklist.md`
