# 34 – Panson Global Best Practices (Consolidated)

Owner: Engineering & Marketing | Review: Quarterly | Last updated: 2025-08-10

Purpose: Consolidate established best practices from this project’s documentation and observed patterns, highlight gaps, and propose targeted improvements specific to Panson Global (Dubai).

Sources indexed: `21-website-architecture.md`, `30-seo-strategy.md`, `19-website-strategy-design.md`, `20-website-content-design.md`, `Panson Global Website Content Blueprint.md`, `18-comprehensive-services-guide.md`, `11-business-logic-analysis.md`, `29-strategy.md`, `05-implementation-roadmap.md`, `100-panson-dubai-worklist.md`, `31-best-practices.md`, `31-bestPractices.md`.

---
## 1) Project Structure & Documentation
- File naming: Numerical prefixes for order and grouping (e.g., `00-*` strategy, `20-*` technical, `200-*` products, `300-*` suppliers).
- Naming style: Hyphenated, descriptive filenames; avoid spaces except where intentional for stakeholder docs.
- Media: Central `media/` with hashed filenames; reference via Obsidian links.
- Document template: Title, brief description, horizontal rule, numbered sections (Exec Summcary → Methodology → Analysis → Recommendations → Implementation → References). See Content Blueprint.
- Governance: Content calendar, quarterly audits, fact-checking and source citations recommended in blueprint; add update metadata (owner, last updated, review cadence).

---
## 2) Architecture & Stack (Established)
- Frontend: Next.js (React 18) + TailwindCSS.
- CMS: Sanity.io headless; structured content, multilingual.
- Search: Algolia for fast, faceted SKU finder.
- Media: Cloudinary for AVIF/WebP, `f_auto,q_auto`.
- Hosting/CDN: Vercel; incremental static regeneration (ISR ≤ 24h), preview deploys.
- Forms/CRM: HubSpot embedded forms; lifecycle mapping and attribution.
- Analytics/UX: GA4; Hotjar/VWO appear in some docs as optional UX tools.
- A/B testing: VWO (noted in architecture; confirm adoption before enabling at scale).

Notes: One early roadmap mentions WordPress/Shopify; canonical technical direction is Next.js + Sanity per `21-website-architecture.md` and SEO plan.

---
## 3) Information Architecture (IA) & Components
- IA: Hub-and-spoke around Solutions (Steel, Glass, Hardware), Services, Suppliers, Markets, Resources, About, Contact.
- Page templates: Hero → Value/Features → Social Proof → Content blocks → FAQ → Footer. Resource articles with TOC and related links.
- Component library: `HeroBanner`, `SolutionCard`, `StatsBar`, `ProductTable`, `LeadForm`, `VideoLightbox`, `Breadcrumbs`, `LocaleSwitcher` with Storybook coverage.

---
## 4) Content & SEO Standards
- Structure: One H1 per page; hierarchical H2/H3; keyword-rich slugs; meta title/description limits.
- Schema: JSON-LD for `Product`, `FAQPage`, `Article`, `Organization`, `BreadcrumbList`.
- Internal linking: Pillars ↔ spokes; breadcrumbs; related resources.
- Assets: Cloudinary images in AVIF/WebP; descriptive alt; lazy-load below the fold.
- Programmatic SEO: Fastener SKU templates with canonical grouping; block faceted params from indexing.
- Performance budgets: LCP < 2.5s, CLS < 0.1, TBT < 200ms on top pages.
- Measurement: GA4, GSC, HubSpot; Looker Studio dashboard for sessions, rankings, leads, CVR.

---
## 5) Localization (i18n)
- Locales: EN / AR / ZH with hreflang and canonicals.
- Workflow: Author EN → machine pass (Transifex plugin) → human review → publish; fallback to EN if incomplete.
- UI: Locale routing, switcher in header; human-reviewed translations for critical pages.

---
## 6) Services Operations (Business Practices)
- Six core services: Supplier Matching, QA & Compliance, Logistics & Inventory, Technical Support, Project Management, Partnership Development.
- Operations highlights: Cross-border QC (CN + GCC), Dubai hub with 48-hour GCC delivery, SLA-backed QA documentation, VMI/consignment options.

---
## 7) Deployment & DevOps
- CI/CD: GitHub → Vercel; PR preview deployments; CMS webhooks trigger ISR revalidation.
- Visual QA: Chromatic + Storybook on PRs for component/regression checks.
- Monitoring: StatusPage/Slack alerts mentioned; recommend consolidating uptime and error tracking.
- Security: Cookie consent (GDPR/UAE PDPL), reCAPTCHA v3 on forms, dependency scanning (Snyk), secrets via Vercel envs, least-privilege access.

---
## 8) Coding Standards & Repo Workflow (Observed & Inferred)
Observed:
- No committed codebase, linter/formatter configs, or repo policies present in this folder.

Inferred standards to adopt (aligned with architecture):
- Language/tooling: TypeScript-first with `strict` enabled.
- Style: ESLint (Next.js + TS + a11y) + Prettier; import/order rules.
- Hooks: Husky + lint-staged for pre-commit (lint, typecheck, format).
- Module boundaries: `apps/` and `packages/` optional if monorepo emerges; otherwise `app/`, `components/`, `lib/`, `schemas/` in Next.js.
- Git workflow: Trunk-based with short-lived feature branches; Conventional Commits; protected `main` with required checks.
- Reviews: CODEOWNERS for `app/`, `schemas/`, `infra/`; PR template with checklist; 1–2 required reviewers.
- Docs: CONTRIBUTING.md for setup, scripts, commit, and PR guidelines.

---
## 9) Testing & QA (Gaps & Recommendations)
Current state:
- Storybook/Chromatic referenced; no explicit test suites or CI gates documented for unit/E2E.

Recommendations:
- Unit: Jest + ts-jest; 70%+ coverage on critical utilities.
- Components: Testing Library + Storybook interaction tests; Chromatic as CI gate for visual diffs.
- E2E: Playwright (preferred) or Cypress for RFQ, search, locale, and form flows.
- Accessibility: axe rules in Storybook; pa11y/Playwright a11y run in CI.
- Performance: Lighthouse CI on Vercel Preview; enforce budgets on key routes.

---
## 10) Development Workflow
- Editorial/content: Brief in Notion → Draft (Docs/MDX) → Sanity publish → QA (preview/Storybook) → Release (Vercel) → Measure.
- Engineering: Feature branch → PR with preview URL and Chromatic status → Review → Merge to `main` → Vercel production.
- Roles: SEO lead, Content strategist, EN/AR/ZH writers, Developer, Designer, QA/Ops.

---
## 11) Analytics & Reporting
- Events: RFQ submit, calculator complete, file download, CTA click, locale switch.
- Dashboards: Monthly Looker Studio; annotate releases in GSC; HubSpot attribution and lifecycle mappings.

---
## 12) Notable Gaps & Improvements
- Source of truth: Confirm Next.js + Sanity as canonical; update any conflicting mentions (e.g., WordPress/Shopify) to avoid ambiguity.
- Repo hygiene: Add ESLint, Prettier, TypeScript config, Husky/lint-staged, tsconfig strict.
- CI: GitHub Actions workflow for install → lint → typecheck → unit + component tests → build; integrate Chromatic and Lighthouse.
- Governance: Add CODEOWNERS, PR/Issue templates, CONTRIBUTING.md; define required checks and branch protections.
- Testing: Implement unit, component, E2E, a11y, and performance checks as above.
- CMS governance: Schema versioning, migrations, validations (required fields, locales, slugs), preview drafts, audit fields (`createdBy`, `updatedBy`).
- Observability: Add Sentry for app and edge; consolidate uptime monitoring; structured logging of key events.

---
## 13) 30/60/90 Plan (Engineering Enablement)
- 30 days
  - Scaffold repo: TypeScript strict, ESLint, Prettier, Husky + lint-staged.
  - Add CODEOWNERS, CONTRIBUTING.md, PR/Issue templates.
  - Set up GitHub Actions; integrate Chromatic and Lighthouse on previews.
  - Initialize Storybook; author core component stories.
- 60 days
  - Add Jest + Testing Library; initial Playwright smoke suite.
  - Implement Sentry; configure GA4 events; set Looker dashboard live.
  - Sanity: introduce schema versioning and content validations.
- 90 days
  - Expand E2E to critical journeys; add a11y CI; enforce performance budgets.
  - Ship programmatic SKU pages with Algolia finder and canonical clusters.

---
## 14) References
- Architecture & operations: `21-website-architecture.md`
- SEO strategy: `30-seo-strategy.md`
- Strategy/design/content: `19-website-strategy-design.md`, `20-website-content-design.md`, `Panson Global Website Content Blueprint.md`
- Services & positioning: `18-comprehensive-services-guide.md`, `11-business-logic-analysis.md`
- Roadmap & AXP: `05-implementation-roadmap.md`, `29-strategy.md`
- Naming/worklist: `100-panson-dubai-worklist.md`
- Prior best-practices: `31-best-practices.md`, `31-bestPractices.md` (see `33-comapres-practies.md` for comparison)

