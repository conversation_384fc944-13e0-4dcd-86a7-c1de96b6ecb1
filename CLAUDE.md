# CLAUDE.md - Panson Global Dubai Project Guide

## Project Overview

This is the Panson Global Dubai business development project - a comprehensive strategy and implementation guide for establishing a Dubai-based industrial supply chain platform. The project focuses on three core product lines: carbon additives, specialized glass systems, and industrial fasteners.

## Project Structure

### Core Strategy Documents (00-series)
- `00-comprehensive-strategy.md` - Main strategic framework and implementation plan
- `00-information-gathering-strategy.md` - Research methodology and data sources
- `00-new-strategy.md`, `00-new.md`, `00-true.md` - Strategy iterations and refinements

### Business Analysis (01-20 series)
- `01-market-research-analysis.md` - GCC market analysis and opportunities
- `02-company-profiles.md` - Supplier and partner company profiles
- `03-product-portfolio.md` - Three-pillar product strategy
- `04-digital-marketing-strategy.md` - Online marketing and SEO approach
- `05-implementation-roadmap.md` - Phased implementation timeline
- `06-executive-summary.md` - High-level business summary
- `07-regulatory-legal-framework.md` - Dubai DMCC legal requirements
- `08-competitive-analysis.md` - Market competition assessment
- `09-financial-projections.md` - Revenue and investment models
- `10-company-introduction.md` - Corporate overview and positioning

### Operations & Implementation (11-30 series)
- `11-business-logic-analysis.md` - Core business operations framework
- `12-blog-content-strategy.md` - Content marketing approach
- `13-16` - Technical blog articles (carbon additives, smart glass, fasteners)
- `17-18` - Service portfolio and comprehensive guides
- `19-22` - Website strategy, architecture, and SEO
- `23-24` - LinkedIn strategy and intelligence hub
- `28-30` - Collections strategy and SEO optimization

### Best Practices & Optimization (31-34 series)
- `31-best-practices.md`, `31-bestPractices.md` - Initial best practices
- `32-compare.md` - Comparative analysis
- `33-comapres-practies.md` - Practice comparisons
- `34-bestPractic.md` - **CURRENT FILE** - Consolidated best practices and technical standards

### Product Documentation (100-400 series)
- `100-104` - Strategic worklists and implementation plans
- `200-series` - Carbon additives product documentation
- `300-series` - Glass products and Wangmei Group portfolio
- `400-series` - Fasteners and hardware specifications

### Social Media & Marketing
- `facebook_posts_1_60.md`, `facebook_strategy_plan.md`
- `linkedin_posts_*.md`, `linkedin_strategy_and_calendar.md`
- `twitter_posts_1_100.md`, `twitter_strategy_plan.md`
- `seo_keyword_research.md`

### Media Assets
- `media/` folder contains product images, company documentation, and visual assets

## Key Technical Standards

Based on the consolidated best practices in `34-bestPractic.md`:

### Architecture Stack
- **Frontend**: Next.js (React 18) + TailwindCSS
- **CMS**: Sanity.io headless CMS
- **Search**: Algolia for product search
- **Media**: Cloudinary for image optimization
- **Hosting**: Vercel with ISR
- **Forms/CRM**: HubSpot integration
- **Analytics**: GA4 + Hotjar

### Development Standards
- **Language**: TypeScript with strict mode
- **Linting**: ESLint + Prettier
- **Git**: Conventional commits, trunk-based workflow
- **Testing**: Jest + Testing Library + Playwright
- **CI/CD**: GitHub Actions + Vercel

### Content Structure
- **Localization**: EN/AR/ZH with hreflang
- **SEO**: Schema markup, performance budgets
- **Components**: Storybook + Chromatic for visual testing

## Current Development Status

The project is in the strategy and documentation phase with:
- ✅ Comprehensive market research completed
- ✅ Business strategy defined
- ✅ Technical architecture planned
- ✅ Content strategy developed
- 🔄 Best practices consolidated (current file: 34-bestPractic.md)
- ⏳ Development implementation pending

## Commands to Run

Based on the technical stack, these commands should be available once development starts:

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Quality Assurance
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript checks
npm run test         # Run unit tests
npm run test:e2e     # Run Playwright tests

# Content Management
npm run storybook    # Start Storybook
npx sanity start     # Start Sanity Studio
```

## Project Goals & Success Metrics

### Financial Targets (Year 1)
- Revenue: $5-10 million
- Gross Margin: 25-35%
- Active Customers: 50+

### Operational Metrics
- On-time delivery: 95%
- Customer satisfaction: 90%+
- Website traffic: 10,000+ monthly visitors

### Digital Performance
- Lead generation: 100+ qualified leads/month
- Conversion rate: 15-20%
- SEO: Top rankings for target keywords

## Next Action Items

1. **Immediate (30 days)**:
   - Finalize technical specifications
   - Set up development environment
   - Begin website development

2. **Short-term (90 days)**:
   - Launch professional website
   - Establish Dubai operations
   - Secure initial customers

3. **Medium-term (6 months)**:
   - Achieve $1M quarterly revenue
   - Build partner network
   - Scale operations across GCC

## Key Contacts & Partnerships

### Manufacturing Partners
- **NX Panson**: Carbon additives and steel materials
- **Wangmei Group**: Specialized glass and window systems
- **Multiple suppliers**: Industrial fasteners and hardware

### Target Markets
- UAE, Saudi Arabia, Qatar (GCC focus)
- Steel mills, construction, manufacturing
- Government infrastructure projects

---

This CLAUDE.md serves as the master navigation guide for the Panson Global Dubai project. All strategy, implementation, and technical decisions should reference the consolidated best practices in `34-bestPractic.md` for consistency.