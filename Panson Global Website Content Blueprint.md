# Panson Global Website Content Blueprint

Comprehensive, action-ready guide combining insights from strategy, services, and blog documents. Use as the single source of truth for structure, layout, and governance of the website.

---

## 1. Core Navigation & IA
Maximum 7 primary links; audience-centric ordering.

| Position | Menu Item | Purpose/Notes |
|----------|-----------|---------------|
| 1 | Home | Value proposition snapshot, credibility proof, CTAs |
| 2 | Solutions | 3 solution categories (Steel, Glass, Hardware) + sub-pages |
| 3 | Services | Six core professional services overview |
| 4 | Suppliers | Chinese supplier network directory & onboarding |
| 5 | Resources | Blog, guides, case studies, downloads |
| 6 | About | Company story, team, credentials |
| 7 | Contact | Multi-channel enquiry + request-quote forms |

Secondary (utility) bar: Language switcher (EN/AR/ZH) · "Request Quote" · "Supplier Application" · "Customer Portal".

Breadcrumbs enabled on all deep pages for SEO and UX.

---

## 2. Page Archetypes & Component Order
Standardise page patterns for development efficiency and consistent UX.

### 2.1 Marketing Page Pattern (Home, Solutions, Services)
1. Hero (H1, sub-headline, primary CTA x1-2)
2. Problem / Need (optional)
3. Value Proposition / Features (cards or columns)
4. Social Proof (logos, testimonials, metrics)
5. Detailed Content Blocks (accordions, tabs, media-text)
6. Secondary CTA strip
7. FAQ (schema-marked)
8. Footer

### 2.2 Resource Article Pattern (Blog, Guides)
1. Cover image + H1
2. Metadata (author, date, reading time)
3. TOC auto-generated
4. Body content (markdown → MDX)
5. Inline CTAs (lead magnets)
6. Related articles

### 2.3 Directory Listing (Suppliers, Case Studies)
Search + filters sidebar | Card/list view | Pagination / infinite scroll

### 2.4 Conversion Forms
Use modular form component with validation & spam-protection. Map to CRM/Zapier.

---

## 3. Content Pillars & Mapping
Derive from `12-blog-content-strategy.md` & services docs.

| Pillar | Description | Key Pages/Assets |
|--------|-------------|------------------|
| Industry Education | Technical guides & best practices | Blog guides 1-5 |
| Market Insights | Trends & forecasts | Blog articles 6-10, market pages |
| Product Knowledge | Specs & comparisons | Solutions sub-pages, blog 11-15 |
| Case Studies | Success stories | Case study pages, testimonials |
| Business Intelligence | Supply-chain, cost optimisation | Blog, whitepapers |

---

## 4. SEO & Structured Data
- One H1 per page; hierarchical H2-H3.
- Keyword-rich slugs e.g. `/solutions/steel-industry`.
- Meta title ≤ 60 chars, description ≤ 155.
- OpenGraph + Twitter meta.
- JSON-LD schema: `Organization`, `BreadcrumbList`, `FAQPage`, `Article`.
- XML sitemap auto-generated and submitted to GSC.

---

## 5. Performance & Accessibility
- Lighthouse targets: Performance > 90, A11y > 95.
- Serve images in AVIF/WebP ≤ 150 KB, lazy-load below fold.
- Minify & bundle CSS/JS, defer non-critical JS.
- Colour contrast WCAG AA, keyboard nav, ARIA labels.

---

## 6. CMS / Repo Structure (Static-Site-Generator-Friendly)
```
/content
  home.md
  about.md
  solutions/
    index.md            # overview
    steel.md
    glass.md
    hardware.md
  services.md
  suppliers.md
  case-studies/
    steel-efficiency-15.md
  blog/
    2024-05-carbon-additives-guide.md
/assets
  images/
  icons/
/layouts
/components
```
Markdown files include YAML front-matter: `title`, `description`, `date`, `tags`, `category`, `thumbnail`, `draft`.

---

## 7. Governance & Workflow
1. **Editorial Calendar**: Plan topics 90 days out.
2. **Content Creation SOP**: Research → Draft → Review → SEO-optimise → Design → Publish.
3. **Quality Control**: Fact-check, source citations, technical review (engineer), plagiarism scan.
4. **Updates**: Quarterly content audit to refresh data and links.
5. **Roles**: Content Strategist, Writer, Designer, Developer, QA.

---

## 8. Next Actions
- Implement navigation & page templates per archetypes.
- Migrate existing markdowns into `/content` tree.
- Build components (hero, card, testimonial, form).
- Connect headless CMS or markdown build pipeline (e.g. Next.js + MDX).
- Launch MVP → iterate via analytics & user feedback.

> This blueprint supersedes earlier drafts (`19-website-strategy-design.md`, `20-website-content-design.md`, etc.) and consolidates them into one actionable document.


Use this guide to structure and organise all content for the Panson Global (Dubai) website.

---

## 1. Primary Navigation
Order items by typical visitor intent. Aim for **≤ 7** top-level links; move overflow into drop-downs.

| Position | Menu Item | Purpose |
|----------|-----------|---------|
| 1 | Home | Snapshot of offerings & latest highlights |
| 2 | About Us | Mission, team, credentials, press |
| 3 | Solutions / Services | Detailed service pages (one per service) |
| 4 | Projects / Case Studies | Showcase past work, metrics, testimonials |
| 5 | Insights / Blog | Thought-leadership articles, news |
| 6 | Resources | White-papers, downloads, tools |
| 7 | Contact | Enquiry form, map, contact info |

If more than 7 items are required, group logically (e.g. **Company** → About, Careers, Press).

---

## 2. Page Layout Pattern
Every core page should follow a predictable hierarchy so users can scan quickly.

1. **Hero Section**  
   • Clear H1 headline expressing value proposition  
   • Sub-headline explaining benefit  
   • Primary Call-to-Action (CTA) button  
2. **Problem / Need** – Short paragraph describing pain-point you solve.
3. **Solution / Features** – Bulleted or card layout, 3–6 points.
4. **Social Proof** – Logos, testimonials, case-study snippets.
5. **Secondary CTA** – e.g. "Schedule a Call".
6. **Supporting Content** – FAQs, technical specs, downloads.
7. **Footer** – Repeat contact options, legal links, social icons.

Apply the same visual rhythm (spacing, fonts, colours) site-wide for consistency.

---

## 3. Header & Footer Checklist
- Sticky header ≤ 80 px height for quick access.
- Mobile menu via hamburger; collapsible mega-menu on desktop if needed.
- Footer contains: quick links, newsletter signup, social icons, copyright, privacy & terms.

---

## 4. SEO & Accessibility Essentials
- Semantic HTML (`<header>`, `<nav>`, `<main>`, `<section>`, `<footer>`).
- One **H1** per page, descriptive **title** & **meta description** ≤ 160 characters.
- Alt text for every image.
- Use keyword-rich slugs: `/services/structural-engineering`.
- Provide **sitemap.xml** and **robots.txt**.

---

## 5. Media & Performance
- Optimise images (<150 KB where possible); serve AVIF/WebP with fallbacks.
- Use SVG for icons/logos.
- Lazy-load below-the-fold assets.
- Minify CSS/JS; bundle & defer non-critical scripts.

---

## 6. Recommended Project Folder Structure
```
/content
  about.md
  contact.md
  services/
    index.md          # overview
    structural.md
    project-management.md
  case-studies/
    2024-05-green-tower.md
  blog/
    2025-08-new-design-trends.md
/assets
  images/
  icons/
/layouts
/components
```
Each Markdown file contains front-matter (`title`, `description`, `date`, `tags`, `thumbnail`, `draft`).

---

## 7. Governance
- Maintain an editorial calendar for the **Insights/Blog** section.
- Use a style guide: tone, spelling (en-UK vs en-US), brand colours, typography.
- Review pages quarterly for outdated content & broken links.

---

## 8. Next Steps
1. Map existing content to the structure above.
2. Identify gaps and create a content backlog.
3. Implement navigation in site templates.
4. Add breadcrumb component for deep pages.
5. Configure CMS or static-site generator to mirror `/content` structure.

---

Feel free to refine sections or request deeper guidance on any part of this blueprint.
