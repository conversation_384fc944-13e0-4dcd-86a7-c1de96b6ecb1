# 21 – Panson Global Website Architecture & Operations Manual

> This document translates the strategic concepts in `19-website-strategy-design.md` and `20-website-content-design.md` into an **actionable blueprint** for designers, developers, and content managers. It covers information architecture, CMS schema, component library, localisation, SEO, analytics, and day-to-day governance.

---
## 1. Tech-Stack Overview
| Layer | Solution | Rationale |
|-------|----------|-----------|
| Front-End | Next.js (React 18) + TailwindCSS | Static generation + dynamic API, rapid development, SEO control |
| CMS | Sanity.io (Headless) | Real-time collaboration, structured content, multilingual support |
| E-Com / RFQ | Snipcart (Cart) + Custom RFQ micro-service (Node + Mongo) | Lightweight checkout for in-stock SKUs; custom form for project-based RFQ |
| Search | Algolia | Blazing-fast faceted search for 5k+ SKUs |
| Hosting | Vercel (Global CDN) | Edge delivery, zero-downtime deployments |
| Images & Video | Cloudinary | Automatic format/size optimisation |
| Forms & CRM | HubSpot Embedded Forms | Single source of truth for leads |
| Analytics | Google Analytics 4 + Hotjar | Traffic & behaviour insights |
| A/B Testing | VWO | Data-driven UI optimisation |

---
## 2. Information Architecture (Sitemap)
```
/
├── solutions/
│   ├── steel/
│   │   ├── carbon-additives
│   │   ├── ferro-alloys
│   │   └── silicon-carbide
│   ├── glass/
│   │   ├── smart-glass
│   │   ├── energy-efficient
│   │   └── window-systems
│   └── hardware/
│       ├── fasteners
│       ├── corrosion-resistant
│       └── custom-solutions
├── services/
│   ├── sourcing
│   ├── quality-control
│   ├── logistics-inventory
│   ├── technical-support
│   ├── project-management
│   └── partnership-development
├── suppliers/  (dual-audience landing)
│   ├── directory (for GCC buyers)
│   ├── apply  (for CN suppliers)
│   └── verification-process
├── markets/
│   ├── uae
│   ├── saudi-arabia
│   ├── qatar
│   └── oman
├── resources/
│   ├── blog
│   ├── case-studies
│   ├── calculators
│   ├── whitepapers
│   └── videos
├── about/
│   ├── company
│   ├── team
│   ├── certifications
│   └── careers
└── contact (multi-step form + map)
```

---
## 3. Component & Template Library
| Component | Props | Usage |
|-----------|-------|-------|
| `HeroBanner` | `title`, `subtitle`, `cta[]`, `bgImage` | Every top-level page |
| `SolutionCard` | `image`, `title`, `desc`, `link` | Homepage solutions grid |
| `StatsBar` | `stats[]` | Homepage trust bar |
| `ProductTable` | `specs`, `downloadLink` | Product detail pages |
| `LeadForm` | `formId`, `trackingParams` | Adjustable per pillar |
| `VideoLightbox` | `youtubeId` | Case study pages |
| `Breadcrumbs` | `path` | All deep pages |
| `LocaleSwitcher` |  | Header |

All components reside in `/components/` with Storybook stories for QA. Design tokens captured in Tailwind config.

---
## 4. Sanity CMS Schema
```js
// /schemas/solution.js
export default {
  name: 'solution',
  type: 'document',
  fields: [
    { name: 'title', type: 'string' },
    { name: 'slug',  type: 'slug',   options: { source: 'title' } },
    { name: 'hero',  type: 'hero' },
    { name: 'overview', type: 'array', of: [{type: 'block'}] },
    { name: 'products', type: 'array', of: [{ type: 'reference', to: [{type: 'product'}] }] },
    { name: 'faqs', type: 'array', of: [{ type: 'faq' }] },
    { name: 'locale', type: 'string', options: { list: ['en', 'ar', 'zh'] } }
  ]
}
```
Repeat for `product`, `service`, `blogPost`, etc. Shared fields go into `object` types (`hero`, `cta`, `seo` metadata).

---
## 5. Localisation Workflow
1. Default content authored in English (`locale: en`).
2. Automated machine-translation pass via Sanity Transifex plug-in (AR + ZH).
3. Human review by in-house bilingual editors (status → `reviewed`).
4. Build pipeline filters by `locale`; fallback to English if translation incomplete.

---
## 6. SEO Technical Checklist
- Static site generation (`getStaticProps`) with incremental revalidation (ISR ≤ 24 h).
- `<head>` meta via Next-Seo: title, description, canonical, social OG.
- `robots.txt` and dynamic sitemap.xml auto-generated on build.
- Structured data (`Product`, `FAQPage`) injected via JSON-LD.
- CLS < 0.1; LCP < 2.5 s; use Cloudinary `f_auto,q_auto`.

---
## 7. Lead Capture & CRM Integration
| Scenario | Form | HubSpot Pipeline Stage |
|----------|------|-----------------------|
| Generic Contact | `/contact` multi-step | *MQL* |
| Product RFQ | Side-panel RFQ form | *SQL – Product* |
| Supplier Application | `/suppliers/apply` | *Partner – Pending* |
All forms embed hidden UTM + `contentId` for attribution. HubSpot webhooks update Sanity doc `leadCount` for case study stats.

---
## 8. Deployment & DevOps
1. GitHub → Vercel CI; main→production, preview PRs auto-deployed.
2. Chromatic runs visual regression tests on each PR.
3. CMS changes deploy instantly via webhooks (ISR revalidation).
4. StatusPage monitors uptime; Slack alerts on failures.

---
## 9. Governance & Content Calendar
| Role | Responsibility | Owner |
|------|----------------|-------|
| Product Marketer | Publishes weekly blog, case study | Marketing |
| SEO Specialist | Monthly technical audit | Marketing |
| Web Developer | Component upkeep, new templates | Tech |
| Translator (EN↔AR) | Bi-weekly localisation | Ops |
| QA Lead | Checks broken links, forms | Ops |

Content pipeline tracked in Notion Kanban (Ideation → Draft → Review → Scheduled → Live).

---
## 10. Analytics Dashboard (Looker Studio)
Metrics Tracked:
- Sessions / Users / Bounce
- Top Landing Pages & CTA click-through
- Form submissions by channel
- Conversion rate by language
- Heatmap video plays (Hotjar)
- Page Speed Core Web Vitals

Monthly reporting meeting 1st Monday; insights feed directly into A/B backlog.

---
## 11. Security & Compliance
- GDPR & UAE PDPL compliant consent banner (Cookiebot).
- reCAPTCHA v3 on all forms.
- Weekly dependency vulnerability scan (Snyk).
- Cloudinary signed URLs for private assets.

---
## 12. Future Modules (Phase-2/3)
1. **Customer Portal** – login for order tracking & document downloads.  
2. **3D Product Configurator** for bespoke glass & hardware.  
3. **Multi-warehouse stock indicator** (Dubai + KSA) via Odoo API.

---
**Document version**: v1.0 – 2024-01-10  
**Maintainer**: Digital Team @ Panson Global
